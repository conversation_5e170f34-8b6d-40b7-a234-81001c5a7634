#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Twist
from ackermann_msgs.msg import AckermannDriveStamped
import math

class CmdVelToAckermann:
    def __init__(self):
        rospy.init_node('cmd_vel_to_ackermann')
        
        # 车辆参数
        self.wheelbase = 0.4  # 轴距 (m)
        self.max_steering_angle = 0.7854  # 最大转向角 (45度)
        
        # 订阅cmd_vel话题
        self.cmd_vel_sub = rospy.Subscriber('/cmd_vel', Twist, self.cmd_vel_callback)
        
        # 发布ackermann命令
        self.ackermann_pub = rospy.Publisher('/ackermann_cmd', AckermannDriveStamped, queue_size=1)
        
        rospy.loginfo("cmd_vel to ackermann converter started")
    
    def cmd_vel_callback(self, msg):
        """将Twist消息转换为Ackermann消息"""
        ackermann_msg = AckermannDriveStamped()
        ackermann_msg.header.stamp = rospy.Time.now()
        ackermann_msg.header.frame_id = "base_link"
        
        # 线速度直接使用
        ackermann_msg.drive.speed = msg.linear.x
        
        # 角速度转换为转向角
        if abs(msg.linear.x) > 0.001:  # 避免除零
            # 使用自行车模型: steering_angle = atan(wheelbase * angular_velocity / linear_velocity)
            steering_angle = math.atan(self.wheelbase * msg.angular.z / msg.linear.x)
            
            # 限制转向角度
            steering_angle = max(-self.max_steering_angle, 
                               min(self.max_steering_angle, steering_angle))
        else:
            steering_angle = 0.0
        
        ackermann_msg.drive.steering_angle = steering_angle
        
        # 发布消息
        self.ackermann_pub.publish(ackermann_msg)
        
        # 调试信息
        rospy.logdebug(f"Linear: {msg.linear.x:.2f}, Angular: {msg.angular.z:.2f}, "
                      f"Steering: {math.degrees(steering_angle):.1f}°")

if __name__ == '__main__':
    try:
        converter = CmdVelToAckermann()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass
