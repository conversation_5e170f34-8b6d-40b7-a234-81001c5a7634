<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="ackermann_robot_gazebo">

  <!-- 引入基础模型 -->
  <xacro:include filename="$(find my_robot_description)/urdf/ackermann_robot.urdf.xacro"/>

  <!-- Gazebo材质 -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
  </gazebo>

  <gazebo reference="front_left_wheel">
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="front_right_wheel">
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="rear_left_wheel">
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="rear_right_wheel">
    <material>Gazebo/DarkGrey</material>
  </gazebo>
  <gazebo reference="front_left_steering_link">
    <material>Gazebo/Red</material>
  </gazebo>
  <gazebo reference="front_right_steering_link">
    <material>Gazebo/Red</material>
  </gazebo>

  <!-- Ackermann转向驱动插件配置 -->
  <gazebo>
    <plugin name="ackermann_drive_controller" filename="libgazebo_ros_ackermann_drive.so">

      <!-- 更新频率 -->
      <update_rate>100.0</update_rate>

      <!-- 驱动轮关节（后轮） -->
      <left_joint>rear_left_wheel_joint</left_joint>
      <right_joint>rear_right_wheel_joint</right_joint>

      <!-- 转向关节（前轮） -->
      <left_steering_joint>front_left_steering_joint</left_steering_joint>
      <right_steering_joint>front_right_steering_joint</right_steering_joint>

      <!-- 车辆几何参数 -->
      <wheel_separation>0.30</wheel_separation>    <!-- 左右轮间距 -->
      <wheel_diameter>0.10</wheel_diameter>        <!-- 轮子直径 -->
      <wheel_base>0.40</wheel_base>               <!-- 前后轮轴距 -->

      <!-- 转向限制 -->
      <max_steer>0.7854</max_steer>               <!-- 最大转向角度（45度） -->

      <!-- 力矩限制 -->
      <max_wheel_torque>500.0</max_wheel_torque>
      <max_wheel_acceleration>5.0</max_wheel_acceleration>

      <!-- 通信话题 -->
      <command_topic>/ackermann_cmd</command_topic>
      <odometry_topic>/odom</odometry_topic>

      <!-- 坐标系设置 -->
      <odometry_frame>odom</odometry_frame>
      <robot_base_frame>base_link</robot_base_frame>

      <!-- 发布选项 -->
      <publish_odom>true</publish_odom>
      <publish_wheel_tf>true</publish_wheel_tf>
      <publish_odom_tf>true</publish_odom_tf>

    </plugin>
  </gazebo>

  <!-- 备用：关节状态发布器 -->
  <gazebo>
    <plugin name="joint_state_publisher" filename="libgazebo_ros_joint_state_publisher.so">
      <update_rate>50.0</update_rate>
      <joint_name>front_left_steering_joint</joint_name>
      <joint_name>front_right_steering_joint</joint_name>
      <joint_name>front_left_wheel_joint</joint_name>
      <joint_name>front_right_wheel_joint</joint_name>
      <joint_name>rear_left_wheel_joint</joint_name>
      <joint_name>rear_right_wheel_joint</joint_name>
    </plugin>
  </gazebo>

  <!-- 轮子摩擦和接触参数 -->
  <gazebo reference="front_left_wheel">
    <mu1>2.0</mu1><mu2>2.0</mu2>
    <kp>1000000.0</kp><kd>1.0</kd>
    <minDepth>0.001</minDepth><maxVel>0.1</maxVel>
  </gazebo>
  <gazebo reference="front_right_wheel">
    <mu1>2.0</mu1><mu2>2.0</mu2>
    <kp>1000000.0</kp><kd>1.0</kd>
    <minDepth>0.001</minDepth><maxVel>0.1</maxVel>
  </gazebo>
  <gazebo reference="rear_left_wheel">
    <mu1>2.0</mu1><mu2>2.0</mu2>
    <kp>1000000.0</kp><kd>1.0</kd>
    <minDepth>0.001</minDepth><maxVel>0.1</maxVel>
  </gazebo>
  <gazebo reference="rear_right_wheel">
    <mu1>2.0</mu1><mu2>2.0</mu2>
    <kp>1000000.0</kp><kd>1.0</kd>
    <minDepth>0.001</minDepth><maxVel>0.1</maxVel>
  </gazebo>

</robot>
