#!/usr/bin/env python3

import rospy
import subprocess
import sys
import os

def test_xacro_files():
    """测试xacro文件是否能正确解析"""
    print("=== 测试XACRO文件解析 ===")
    
    # 测试基础模型
    try:
        result = subprocess.run(['xacro', 'ackermann_robot.urdf.xacro'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ ackermann_robot.urdf.xacro 解析成功")
        else:
            print("✗ ackermann_robot.urdf.xacro 解析失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试基础模型时出错: {e}")
        return False
    
    # 测试Gazebo模型
    try:
        result = subprocess.run(['xacro', 'ackermann_robot_gazebo.urdf.xacro'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ ackermann_robot_gazebo.urdf.xacro 解析成功")
        else:
            print("✗ ackermann_robot_gazebo.urdf.xacro 解析失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"✗ 测试Gazebo模型时出错: {e}")
        return False
    
    return True

def check_urdf_validity():
    """检查URDF文件的有效性"""
    print("\n=== 检查URDF有效性 ===")
    
    try:
        # 生成URDF文件
        result = subprocess.run(['xacro', 'ackermann_robot.urdf.xacro', '-o', 'temp_robot.urdf'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("✗ 无法生成URDF文件")
            return False
        
        # 使用check_urdf检查
        result = subprocess.run(['check_urdf', 'temp_robot.urdf'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ URDF文件结构有效")
        else:
            print("✗ URDF文件结构无效:")
            print(result.stderr)
            return False
            
        # 清理临时文件
        if os.path.exists('temp_robot.urdf'):
            os.remove('temp_robot.urdf')
            
    except Exception as e:
        print(f"✗ 检查URDF有效性时出错: {e}")
        return False
    
    return True

def analyze_model_structure():
    """分析模型结构"""
    print("\n=== 模型结构分析 ===")
    
    try:
        # 生成URDF并分析
        result = subprocess.run(['xacro', 'ackermann_robot.urdf.xacro'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("✗ 无法生成URDF进行分析")
            return False
        
        urdf_content = result.stdout
        
        # 统计链接和关节
        links = urdf_content.count('<link name=')
        joints = urdf_content.count('<joint name=')
        
        print(f"✓ 模型包含 {links} 个链接")
        print(f"✓ 模型包含 {joints} 个关节")
        
        # 检查关键组件
        required_links = ['base_link', 'front_left_wheel', 'front_right_wheel', 
                         'rear_left_wheel', 'rear_right_wheel', 
                         'front_left_steering_link', 'front_right_steering_link']
        
        missing_links = []
        for link in required_links:
            if f'<link name="{link}"' not in urdf_content:
                missing_links.append(link)
        
        if missing_links:
            print(f"✗ 缺少必要的链接: {missing_links}")
            return False
        else:
            print("✓ 所有必要的链接都存在")
        
        # 检查关键关节
        required_joints = ['front_left_steering_joint', 'front_right_steering_joint',
                          'front_left_wheel_joint', 'front_right_wheel_joint',
                          'rear_left_wheel_joint', 'rear_right_wheel_joint']
        
        missing_joints = []
        for joint in required_joints:
            if f'<joint name="{joint}"' not in urdf_content:
                missing_joints.append(joint)
        
        if missing_joints:
            print(f"✗ 缺少必要的关节: {missing_joints}")
            return False
        else:
            print("✓ 所有必要的关节都存在")
            
    except Exception as e:
        print(f"✗ 分析模型结构时出错: {e}")
        return False
    
    return True

def main():
    print("Ackermann四轮小车模型测试工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('ackermann_robot.urdf.xacro'):
        print("✗ 找不到 ackermann_robot.urdf.xacro 文件")
        sys.exit(1)
    
    if not os.path.exists('ackermann_robot_gazebo.urdf.xacro'):
        print("✗ 找不到 ackermann_robot_gazebo.urdf.xacro 文件")
        sys.exit(1)
    
    # 运行测试
    tests_passed = 0
    total_tests = 3
    
    if test_xacro_files():
        tests_passed += 1
    
    if check_urdf_validity():
        tests_passed += 1
    
    if analyze_model_structure():
        tests_passed += 1
    
    # 总结
    print("\n" + "=" * 50)
    print(f"测试完成: {tests_passed}/{total_tests} 项测试通过")
    
    if tests_passed == total_tests:
        print("✓ 所有测试通过！模型可以使用。")
        print("\n建议的下一步:")
        print("1. 启动Gazebo测试: roslaunch test_ackermann.launch")
        print("2. 使用键盘控制: rosrun teleop_twist_keyboard teleop_twist_keyboard.py")
        print("3. 运行消息转换器: python3 cmd_vel_to_ackermann.py")
    else:
        print("✗ 部分测试失败，请检查模型文件。")
        sys.exit(1)

if __name__ == '__main__':
    main()
