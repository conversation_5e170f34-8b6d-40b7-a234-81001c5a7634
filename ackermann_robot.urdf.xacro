<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="ackermann_robot">

  <!-- 定义颜色材质 -->
  <material name="blue">
    <color rgba="0.2 0.2 0.8 1"/>
  </material>
  
  <material name="black">
    <color rgba="0.1 0.1 0.1 1"/>  
  </material>
  
  <material name="white">
    <color rgba="0.9 0.9 0.9 1"/>
  </material>

  <material name="red">
    <color rgba="0.8 0.2 0.2 1"/>
  </material>

  <!-- 底盘 -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="0.5 0.3 0.1"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.5 0.3 0.1"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="15.0"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.1875" ixy="0" ixz="0" iyy="0.5208" iyz="0" izz="0.6458"/>
    </inertial>
  </link>

  <!-- 前左转向连杆 -->
  <link name="front_left_steering_link">
    <visual>
      <geometry>
        <cylinder radius="0.02" length="0.05"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.02" length="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000104" ixy="0" ixz="0" iyy="0.000104" iyz="0" izz="0.00004"/>
    </inertial>
  </link>

  <!-- 前右转向连杆 -->
  <link name="front_right_steering_link">
    <visual>
      <geometry>
        <cylinder radius="0.02" length="0.05"/>
      </geometry>
      <material name="red"/>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.02" length="0.05"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000104" ixy="0" ixz="0" iyy="0.000104" iyz="0" izz="0.00004"/>
    </inertial>
  </link>

  <!-- 前左轮 - 修正轮子方向 -->
  <link name="front_left_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000625" ixy="0" ixz="0" iyy="0.001125" iyz="0" izz="0.000625"/>
    </inertial>
  </link>

  <!-- 前右轮 - 修正轮子方向 -->
  <link name="front_right_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000625" ixy="0" ixz="0" iyy="0.001125" iyz="0" izz="0.000625"/>
    </inertial>
  </link>

  <!-- 后左轮 - 修正轮子方向 -->
  <link name="rear_left_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000625" ixy="0" ixz="0" iyy="0.001125" iyz="0" izz="0.000625"/>
    </inertial>
  </link>

  <!-- 后右轮 - 修正轮子方向 -->
  <link name="rear_right_wheel">
    <visual>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
      <material name="black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="1.5708 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.03"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.5"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.000625" ixy="0" ixz="0" iyy="0.001125" iyz="0" izz="0.000625"/>
    </inertial>
  </link>

  <!-- 虚拟方向盘 -->
  <link name="steering_wheel_link">
    <visual>
      <geometry>
        <cylinder radius="0.01" length="0.01"/>
      </geometry>
      <material name="white"/>
    </visual>
    <inertial>
      <mass value="0.01"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
  </link>

  <!-- 关节定义 -->
  
  <!-- 前左转向关节 - 修正高度避免几何干涉 -->
  <joint name="front_left_steering_joint" type="revolute">
    <parent link="base_link"/>
    <child link="front_left_steering_link"/>
    <origin xyz="0.2 0.15 -0.10" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-0.7854" upper="0.7854" effort="100" velocity="1"/>
  </joint>

  <!-- 前右转向关节 - 修正高度避免几何干涉 -->
  <joint name="front_right_steering_joint" type="revolute">
    <parent link="base_link"/>
    <child link="front_right_steering_link"/>
    <origin xyz="0.2 -0.15 -0.10" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-0.7854" upper="0.7854" effort="100" velocity="1"/>
  </joint>

  <!-- 前左轮关节 - 轴向为Y轴 -->
  <joint name="front_left_wheel_joint" type="continuous">
    <parent link="front_left_steering_link"/>
    <child link="front_left_wheel"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <!-- 前右轮关节 - 轴向为Y轴 -->
  <joint name="front_right_wheel_joint" type="continuous">
    <parent link="front_right_steering_link"/>
    <child link="front_right_wheel"/>
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <!-- 后左轮关节 - 统一轴向为Y轴 -->
  <joint name="rear_left_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="rear_left_wheel"/>
    <origin xyz="-0.2 0.15 -0.10" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <!-- 后右轮关节 - 统一轴向为Y轴 -->
  <joint name="rear_right_wheel_joint" type="continuous">
    <parent link="base_link"/>
    <child link="rear_right_wheel"/>
    <origin xyz="-0.2 -0.15 -0.10" rpy="0 0 0"/>
    <axis xyz="0 1 0"/>
  </joint>

  <!-- 虚拟方向盘关节 -->
  <joint name="steering_wheel_joint" type="revolute">
    <parent link="base_link"/>
    <child link="steering_wheel_link"/>
    <origin xyz="0.1 0 0.1" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit lower="-0.7854" upper="0.7854" effort="10" velocity="1"/>
  </joint>

</robot>
