<?xml version="1.0"?>
<launch>
  <!-- 参数设置 -->
  <arg name="model" default="$(find my_robot_description)/urdf/ackermann_robot_gazebo.urdf.xacro"/>
  <arg name="rvizconfig" default="$(find my_robot_description)/rviz/ackermann_robot.rviz"/>
  <arg name="use_sim_time" default="true"/>
  <arg name="gui" default="true"/>
  <arg name="headless" default="false"/>
  <arg name="debug" default="false"/>

  <!-- 设置使用仿真时间 -->
  <param name="use_sim_time" value="$(arg use_sim_time)"/>

  <!-- 启动Gazebo -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="worlds/empty.world"/>
    <arg name="debug" value="$(arg debug)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="paused" value="false"/>
    <arg name="use_sim_time" value="$(arg use_sim_time)"/>
    <arg name="headless" value="$(arg headless)"/>
  </include>

  <!-- 加载机器人描述 -->
  <param name="robot_description" command="$(find xacro)/xacro $(arg model)"/>

  <!-- 在Gazebo中生成机器人 -->
  <node name="urdf_spawner" pkg="gazebo_ros" type="spawn_model" respawn="false" output="screen"
        args="-urdf -model ackermann_robot -param robot_description"/>

  <!-- 发布机器人状态 -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"
        respawn="false" output="screen">
    <remap from="/joint_states" to="/joint_states"/>
  </node>

  <!-- 启动RViz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(arg rvizconfig)" required="false"/>

  <!-- 键盘控制节点（可选） -->
  <node pkg="teleop_twist_keyboard" type="teleop_twist_keyboard.py" name="teleop_keyboard" output="screen">
    <remap from="cmd_vel" to="/ackermann_cmd"/>
  </node>

</launch>
